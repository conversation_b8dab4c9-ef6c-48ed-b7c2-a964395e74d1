package common

import (
	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common/ctxkey"
)

type TaskRelayInfo struct {
	UserId          int
	ChannelId       int
	TokenId         int
	ChannelType     int
	BaseUrl         string
	ApiKey          string
	OriginModelName string
	Action          string
	OriginTaskID    string
	ConsumeQuota    bool
}

func GenTaskRelayInfo(c *gin.Context) *TaskRelayInfo {
	info := &TaskRelayInfo{
		UserId:          c.GetInt(ctxkey.Id),
		ChannelId:       c.GetInt(ctxkey.ChannelId),
		TokenId:         c.GetInt(ctxkey.TokenId),
		ChannelType:     c.GetInt(ctxkey.Channel),
		BaseUrl:         c.GetString(ctxkey.BaseURL),
		ApiKey:          c.GetString(ctxkey.ChannelKey),
		OriginModelName: c.GetString(ctxkey.OriginalModel),
	}
	return info
}

type TaskInfo struct {
	Code   int    `json:"code"`
	TaskID string `json:"task_id"`
	Status string `json:"status"`
	Reason string `json:"reason,omitempty"`
	Url    string `json:"url,omitempty"`
}
