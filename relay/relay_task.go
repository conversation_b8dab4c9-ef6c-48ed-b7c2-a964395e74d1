package relay

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/constant"
	"github.com/songquanpeng/one-api/dto"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/billing"
	"github.com/songquanpeng/one-api/relay/billing/ratio"
	relaycommon "github.com/songquanpeng/one-api/relay/common"
	relayconstant "github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/service"
)

/*
Task 任务通过平台、Action 区分任务
*/
func RelayTaskSubmit(c *gin.Context, relayMode int) (taskErr *dto.TaskError) {
	ctx := c.Request.Context()
	platform := constant.TaskPlatform(c.GetString("platform"))
	relayInfo := relaycommon.GenTaskRelayInfo(c)

	// 使用增强的适配器选择逻辑，考虑渠道类型
	adaptor := GetTaskAdaptorWithInfo(platform, relayInfo)
	if adaptor == nil {
		return service.TaskErrorWrapperLocal(fmt.Errorf("invalid api platform: %s", platform), "invalid_api_platform", http.StatusBadRequest)
	}
	adaptor.Init(relayInfo)
	// get & validate taskRequest 获取并验证文本请求
	taskErr = adaptor.ValidateRequestAndSetAction(c, relayInfo)
	if taskErr != nil {
		return
	}

	// 从gin.Context中获取计费相关信息
	billingType := c.GetInt("billing_type")
	tokenName := c.GetString("token_name")
	tokenKey := c.GetString("token_key")
	tokenGroup := c.GetString("token_group")
	channelName := c.GetString("channel_name")
	requestId := c.GetString("request_id")
	ip := c.GetString("ip")
	remoteIp := c.GetString("remote_ip")

	// 获取模型倍率和用户个性化倍率 - 参考text.go实现
	modelRatio := ratio.GetModelRatio(relayInfo.OriginModelName, relayInfo.ChannelType)
	// 获取用户个性化费率
	userModelRatio, ok, _, err := model.CacheGetUserModelRatio(relayInfo.UserId, relayInfo.OriginModelName)
	if ok {
		modelRatio = userModelRatio
	}

	// 获取分组倍率
	userGroup := c.GetString("group")
	groupRatio := ratio.GetGroupRatio(userGroup)

	// 计算基础倍率
	baseRatio := modelRatio * groupRatio
	if billingType == common.BillingTypeByCount {
		baseRatio = groupRatio // 按次计费时只使用分组倍率
	}

	// 获取充值转换率和用户折扣 - 使用billing包的方法
	tokenGroup = c.GetString("token_group")
	var topupConvertRatio float64 = 1.0
	var userDiscount float64 = 1.0
	if tokenGroup != "" {
		topupConvertRatio = billing.GetTopupConvertRatioByGroupAndTokenGroup(userGroup, tokenGroup)
		userDiscount = billing.GetUserGroupDiscount(relayInfo.UserId, tokenGroup)
	} else {
		userDiscount = billing.GetUserGroupDiscount(relayInfo.UserId, userGroup)
	}

	// 计算配额
	var quota int64
	var costQuota int64

	if billingType == common.BillingTypeByCount { // 按次计费
		// 按次计费 - 获取模型固定价格
		modelFixedPrice, err := ratio.GetModelFixedPrice(relayInfo.OriginModelName)
		if err != nil {
			logger.SysError(fmt.Sprintf("获取模型固定价格失败: %s, 错误: %v", relayInfo.OriginModelName, err))
			// 使用默认价格
			modelFixedPrice = 2.0 // VEO默认价格
		}
		quota = int64(modelFixedPrice * 500000 * groupRatio * topupConvertRatio * userDiscount)
		costQuota = int64(modelFixedPrice * 500000)
	} else {
		// 按token计费 - 视频生成任务默认配额
		quota = int64(baseRatio * 5000 * topupConvertRatio * userDiscount) // 5000为视频生成基础配额
		costQuota = int64(5000)                                            // 成本配额
	}

	// 检查用户配额是否足够
	userQuota, _, err := model.CacheGetUserQuotaAndExpireTime(ctx, relayInfo.UserId)
	if err != nil {
		return service.TaskErrorWrapper(err, "get_user_quota_failed", http.StatusInternalServerError)
	}
	if userQuota < quota {
		return service.TaskErrorWrapperLocal(fmt.Errorf("quota_not_enough"), "quota_not_enough", http.StatusForbidden)
	}

	// 预扣费
	err, _ = model.PreConsumeTokenQuota(ctx, relayInfo.TokenId, quota)
	if err != nil {
		return service.TaskErrorWrapper(err, "pre_consume_token_quota_failed", http.StatusForbidden)
	}

	// build body
	requestBody, err := adaptor.BuildRequestBody(c, relayInfo)
	if err != nil {
		// 预扣费失败，返还配额
		model.PostConsumeTokenQuota(relayInfo.TokenId, -quota)
		taskErr = service.TaskErrorWrapper(err, "build_request_failed", http.StatusInternalServerError)
		return
	}

	// do request
	resp, err := adaptor.DoRequest(c, relayInfo, requestBody)
	if err != nil {
		// 请求失败，返还配额
		model.PostConsumeTokenQuota(relayInfo.TokenId, -quota)
		taskErr = service.TaskErrorWrapper(err, "do_request_failed", http.StatusInternalServerError)
		return
	}

	// handle response
	if resp != nil && resp.StatusCode != http.StatusOK {
		responseBody, _ := io.ReadAll(resp.Body)
		// 请求失败，返还配额
		model.PostConsumeTokenQuota(relayInfo.TokenId, -quota)
		taskErr = service.TaskErrorWrapper(fmt.Errorf(string(responseBody)), "fail_to_fetch_task", resp.StatusCode)
		return
	}

	taskID, taskData, taskErr := adaptor.DoResponse(c, resp, relayInfo)
	if taskErr != nil {
		// 响应处理失败，返还配额
		model.PostConsumeTokenQuota(relayInfo.TokenId, -quota)
		return
	}

	// insert task
	task := model.InitTask(platform, relayInfo)
	task.TaskID = taskID
	task.Quota = int(quota)
	task.Data = taskData
	task.Action = relayInfo.Action
	err = task.Insert()
	if err != nil {
		// 任务插入失败，返还配额
		model.PostConsumeTokenQuota(relayInfo.TokenId, -quota)
		taskErr = service.TaskErrorWrapper(err, "insert_task_failed", http.StatusInternalServerError)
		return
	}

	// 成功创建任务后的计费处理
	defer func() {
		// 更新用户和渠道的使用量
		model.UpdateUserUsedQuotaAndRequestCount(relayInfo.UserId, quota)
		model.UpdateChannelUsedQuota(relayInfo.ChannelId, quota)

		// 记录消费日志
		var logContent string
		if billingType == common.BillingTypeByCount { // 按次计费
			modelFixedPrice, _ := ratio.GetModelFixedPrice(relayInfo.OriginModelName)
			logContent = fmt.Sprintf("异步任务创建成功（按次计费），模型固定价格 %.6g，分组倍率 %.2g，充值转换率 %.4g，用户折扣率 %.2f，任务ID: %s",
				modelFixedPrice, groupRatio, topupConvertRatio, userDiscount, taskID)
		} else {
			logContent = fmt.Sprintf("异步任务创建成功（按量计费），模型倍率 %.4g，分组倍率 %.2g，充值转换率 %.4g，用户折扣率 %.2f，任务ID: %s",
				modelRatio, groupRatio, topupConvertRatio, userDiscount, taskID)
		}

		// 记录详细的消费日志
		model.RecordConsumeLogByDetailIfZeroQuota(
			ctx,
			requestId,
			ip,
			remoteIp,
			relayInfo.UserId,
			relayInfo.ChannelId,
			0, // 异步任务没有输入token
			0, // 异步任务没有输出token
			relayInfo.OriginModelName,
			tokenName,
			tokenKey,
			tokenGroup,
			channelName,
			int(quota),
			int(costQuota),
			0, // 异步任务没有请求时长
			0,
			0,
			true,
			logContent,
			"",
		)

		logger.Info(ctx, fmt.Sprintf("异步任务创建成功，任务ID: %s，消耗配额: %d", taskID, quota))
	}()

	return nil
}

var fetchRespBuilders = map[int]func(c *gin.Context) (respBody []byte, taskResp *dto.TaskError){
	relayconstant.RelayModeVertexVideoFetchByID: videoFetchByIDRespBodyBuilder,
}

func RelayTaskFetch(c *gin.Context, relayMode int) (taskResp *dto.TaskError) {
	respBuilder, ok := fetchRespBuilders[relayMode]
	if !ok {
		taskResp = service.TaskErrorWrapperLocal(errors.New("invalid_relay_mode"), "invalid_relay_mode", http.StatusBadRequest)
		return
	}

	respBody, taskErr := respBuilder(c)
	if taskErr != nil {
		return taskErr
	}

	c.Writer.Header().Set("Content-Type", "application/json")
	_, err := io.Copy(c.Writer, bytes.NewBuffer(respBody))
	if err != nil {
		taskResp = service.TaskErrorWrapper(err, "copy_response_body_failed", http.StatusInternalServerError)
		return
	}
	return
}

func videoFetchByIDRespBodyBuilder(c *gin.Context) (respBody []byte, taskResp *dto.TaskError) {
	taskId := c.Param("task_id")
	userId := c.GetInt("id")

	originTask, exist, err := model.GetByTaskId(userId, taskId)
	if err != nil {
		taskResp = service.TaskErrorWrapper(err, "get_task_failed", http.StatusInternalServerError)
		return
	}
	if !exist {
		taskResp = service.TaskErrorWrapperLocal(errors.New("task_not_exist"), "task_not_exist", http.StatusBadRequest)
		return
	}

	respBody, err = json.Marshal(dto.TaskResponse[any]{
		Code: "success",
		Data: TaskModel2Dto(originTask),
	})
	return
}

func TaskModel2Dto(task *model.Task) *dto.TaskDto {
	return &dto.TaskDto{
		TaskID:     task.TaskID,
		Action:     task.Action,
		Status:     string(task.Status),
		FailReason: task.FailReason,
		ResultUrl:  task.ResultUrl,
		SubmitTime: task.SubmitTime,
		StartTime:  task.StartTime,
		FinishTime: task.FinishTime,
		Progress:   task.Progress,
		Data:       task.Data,
	}
}
