package veo

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
)

var ModelList = []string{
	"veo-3.0-generate-preview",
	"veo-2.0-generate-preview",
	"veo-1.0-generate-preview",
}

type Adaptor struct{}

func (a *Adaptor) ConvertRequest(c *gin.Context, relayMode int, request *model.GeneralOpenAIRequest) (any, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	// Extract video request from the general request
	videoRequest, err := extractVideoRequest(request)
	if err != nil {
		return nil, fmt.Errorf("failed to extract video request: %w", err)
	}

	// Convert to VEO request format
	veoRequest, err := convertToVeoRequest(videoRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to convert to VEO request: %w", err)
	}

	return veoRequest, nil
}

// ConvertOperationRequest converts operation status check request
func (a *Adaptor) ConvertOperationRequest(operationName string) *VeoOperationRequest {
	return &VeoOperationRequest{
		OperationName: operationName,
	}
}

func (a *Adaptor) DoResponse(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, err *model.ErrorWithStatusCode) {
	// Read response body
	body, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		return nil, &model.ErrorWithStatusCode{
			StatusCode: http.StatusInternalServerError,
			Error: model.Error{
				Message: fmt.Sprintf("failed to read response body: %v", readErr),
			},
		}
	}

	// Parse VEO response
	var veoResp VeoResponse
	if err := json.Unmarshal(body, &veoResp); err != nil {
		return nil, &model.ErrorWithStatusCode{
			StatusCode: http.StatusInternalServerError,
			Error: model.Error{
				Message: fmt.Sprintf("failed to parse VEO response: %v", err),
			},
		}
	}

	// Check for errors in response
	if veoResp.Error != nil {
		return nil, &model.ErrorWithStatusCode{
			StatusCode: veoResp.Error.Code,
			Error: model.Error{
				Message: veoResp.Error.Message,
			},
		}
	}

	// Convert to OpenAI-compatible response
	openaiResp := convertVeoToOpenAIResponse(&veoResp)

	// Write response
	c.Header("Content-Type", "application/json")
	c.JSON(resp.StatusCode, openaiResp)

	// Return usage information
	usage = &model.Usage{
		PromptTokens:     1, // VEO doesn't provide token usage, use placeholder
		CompletionTokens: 1,
		TotalTokens:      2,
	}

	return usage, nil
}

// extractVideoRequest extracts video request from general OpenAI request
func extractVideoRequest(request *model.GeneralOpenAIRequest) (*model.VideoRequest, error) {
	videoReq := &model.VideoRequest{
		Model: request.Model,
	}

	// Extract prompt from messages
	if len(request.Messages) > 0 {
		lastMessage := request.Messages[len(request.Messages)-1]
		if content, ok := lastMessage.Content.(string); ok {
			videoReq.Prompt = content
		} else if contentArray, ok := lastMessage.Content.([]interface{}); ok {
			// Handle multi-modal content
			for _, item := range contentArray {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if itemMap["type"] == "text" {
						if text, ok := itemMap["text"].(string); ok {
							videoReq.Prompt = text
						}
					} else if itemMap["type"] == "image_url" {
						// Handle image input for 图片生成视频
						if imageUrl, ok := itemMap["image_url"].(map[string]interface{}); ok {
							if url, ok := imageUrl["url"].(string); ok {
								videoReq.Image = &model.Image{Url: url}
							}
						}
					} else if itemMap["type"] == "last_frame_url" {
						// Handle last frame input for 视频垫图
						if lastFrameUrl, ok := itemMap["last_frame_url"].(map[string]interface{}); ok {
							if url, ok := lastFrameUrl["url"].(string); ok {
								videoReq.LastFrame = &model.Image{Url: url}
							}
						}
					} else if itemMap["type"] == "video_url" {
						// Handle video input for 视频延长
						if videoUrl, ok := itemMap["video_url"].(map[string]interface{}); ok {
							if url, ok := videoUrl["url"].(string); ok {
								videoReq.Video = &model.Video{Url: url}
							}
						}
					}
				}
			}
		}
	}

	// Set default values
	if videoReq.DurationSeconds == 0 {
		videoReq.DurationSeconds = 8
	}
	if videoReq.AspectRatio == "" {
		videoReq.AspectRatio = "16:9"
	}
	if videoReq.SampleCount == 0 {
		videoReq.SampleCount = 1
	}
	if videoReq.PersonGeneration == "" {
		videoReq.PersonGeneration = "allow_all"
	}

	// Set default boolean values
	if videoReq.EnhancePrompt == nil {
		enhance := true
		videoReq.EnhancePrompt = &enhance
	}
	if videoReq.AddWatermark == nil {
		watermark := true
		videoReq.AddWatermark = &watermark
	}
	if videoReq.IncludeRaiReason == nil {
		rai := true
		videoReq.IncludeRaiReason = &rai
	}
	if videoReq.GenerateAudio == nil {
		audio := true
		videoReq.GenerateAudio = &audio
	}

	return videoReq, nil
}

// convertToVeoRequest converts VideoRequest to VEO API format
func convertToVeoRequest(videoReq *model.VideoRequest) (*VeoRequest, error) {
	veoReq := &VeoRequest{
		Instances: []VeoInstance{},
		Parameters: VeoParameters{
			AspectRatio:      videoReq.AspectRatio,
			SampleCount:      videoReq.SampleCount,
			DurationSeconds:  strconv.Itoa(videoReq.DurationSeconds),
			PersonGeneration: videoReq.PersonGeneration,
		},
	}

	// Set boolean parameters
	if videoReq.AddWatermark != nil {
		veoReq.Parameters.AddWatermark = *videoReq.AddWatermark
	}
	if videoReq.IncludeRaiReason != nil {
		veoReq.Parameters.IncludeRaiReason = *videoReq.IncludeRaiReason
	}
	if videoReq.GenerateAudio != nil {
		veoReq.Parameters.GenerateAudio = *videoReq.GenerateAudio
	}
	if videoReq.EnhancePrompt != nil {
		veoReq.Parameters.EnhancePrompt = *videoReq.EnhancePrompt
	}

	// Set optional parameters
	if videoReq.NegativePrompt != "" {
		veoReq.Parameters.NegativePrompt = videoReq.NegativePrompt
	}
	if videoReq.Seed != nil {
		veoReq.Parameters.Seed = videoReq.Seed
	}
	if videoReq.StorageUri != "" {
		veoReq.Parameters.StorageUri = videoReq.StorageUri
	}

	// Create instance
	instance := VeoInstance{}

	if videoReq.Prompt != "" {
		instance.Prompt = videoReq.Prompt
	}

	// Handle image input (图片生成视频)
	if videoReq.Image != nil {
		if videoReq.Image.Url != "" {
			// Convert image URL to base64 if needed
			if strings.HasPrefix(videoReq.Image.Url, "data:") {
				// Extract base64 data from data URL
				parts := strings.Split(videoReq.Image.Url, ",")
				if len(parts) == 2 {
					instance.Image = &VeoImage{
						BytesBase64Encoded: parts[1],
						MimeType:           extractMimeTypeFromDataURL(videoReq.Image.Url),
					}
				}
			} else if strings.HasPrefix(videoReq.Image.Url, "gs://") {
				// Handle GCS URI
				instance.Image = &VeoImage{
					GcsUri:   videoReq.Image.Url,
					MimeType: videoReq.Image.MimeType,
				}
				if instance.Image.MimeType == "" {
					instance.Image.MimeType = "image/jpeg" // default
				}
			}
		} else if videoReq.Image.BytesBase64Encoded != "" {
			instance.Image = &VeoImage{
				BytesBase64Encoded: videoReq.Image.BytesBase64Encoded,
				MimeType:           videoReq.Image.MimeType,
			}
		}
	}

	// Handle last frame input (视频垫图 - 最后一帧)
	if videoReq.LastFrame != nil {
		if videoReq.LastFrame.Url != "" {
			if strings.HasPrefix(videoReq.LastFrame.Url, "data:") {
				// Extract base64 data from data URL
				parts := strings.Split(videoReq.LastFrame.Url, ",")
				if len(parts) == 2 {
					instance.LastFrame = &VeoImage{
						BytesBase64Encoded: parts[1],
						MimeType:           extractMimeTypeFromDataURL(videoReq.LastFrame.Url),
					}
				}
			} else if strings.HasPrefix(videoReq.LastFrame.Url, "gs://") {
				// Handle GCS URI
				instance.LastFrame = &VeoImage{
					GcsUri:   videoReq.LastFrame.Url,
					MimeType: videoReq.LastFrame.MimeType,
				}
				if instance.LastFrame.MimeType == "" {
					instance.LastFrame.MimeType = "image/jpeg" // default
				}
			}
		} else if videoReq.LastFrame.BytesBase64Encoded != "" {
			instance.LastFrame = &VeoImage{
				BytesBase64Encoded: videoReq.LastFrame.BytesBase64Encoded,
				MimeType:           videoReq.LastFrame.MimeType,
			}
		}
	}

	// Handle video input (视频延长)
	if videoReq.Video != nil {
		if videoReq.Video.Url != "" {
			if strings.HasPrefix(videoReq.Video.Url, "data:") {
				// Extract base64 data from data URL
				parts := strings.Split(videoReq.Video.Url, ",")
				if len(parts) == 2 {
					instance.Video = &VeoVideo{
						BytesBase64Encoded: parts[1],
						MimeType:           extractMimeTypeFromDataURL(videoReq.Video.Url),
					}
				}
			} else if strings.HasPrefix(videoReq.Video.Url, "gs://") {
				// Handle GCS URI
				instance.Video = &VeoVideo{
					GcsUri:   videoReq.Video.Url,
					MimeType: videoReq.Video.MimeType,
				}
				if instance.Video.MimeType == "" {
					instance.Video.MimeType = "video/mp4" // default
				}
			}
		} else if videoReq.Video.BytesBase64Encoded != "" {
			instance.Video = &VeoVideo{
				BytesBase64Encoded: videoReq.Video.BytesBase64Encoded,
				MimeType:           videoReq.Video.MimeType,
			}
		}
	}

	veoReq.Instances = append(veoReq.Instances, instance)

	return veoReq, nil
}

// extractMimeTypeFromDataURL extracts MIME type from data URL
func extractMimeTypeFromDataURL(dataURL string) string {
	if strings.HasPrefix(dataURL, "data:") {
		parts := strings.Split(dataURL, ";")
		if len(parts) > 0 {
			return strings.TrimPrefix(parts[0], "data:")
		}
	}
	return "image/jpeg" // default
}

// convertVeoToOpenAIResponse converts VEO response to OpenAI-compatible format
func convertVeoToOpenAIResponse(veoResp *VeoResponse) map[string]interface{} {
	response := map[string]interface{}{
		"id":      veoResp.Name,
		"object":  "video.generation",
		"created": 0, // VEO doesn't provide timestamp
		"model":   "veo",
	}

	if veoResp.Done {
		response["status"] = "completed"
		if veoResp.Response != nil {
			videos := make([]map[string]interface{}, 0)

			// Handle new format (videos array)
			for _, video := range veoResp.Response.Videos {
				videoData := map[string]interface{}{
					"url": video.Uri,
				}
				if video.BytesBase64Encoded != "" {
					videoData["b64_json"] = video.BytesBase64Encoded
				}
				videos = append(videos, videoData)
			}

			// Handle old format (generatedSamples)
			for _, sample := range veoResp.Response.GeneratedSamples {
				videoData := map[string]interface{}{
					"url": sample.Video.Uri,
				}
				if sample.Video.BytesBase64Encoded != "" {
					videoData["b64_json"] = sample.Video.BytesBase64Encoded
				}
				videos = append(videos, videoData)
			}

			response["data"] = videos
		}
	} else {
		response["status"] = "processing"
	}

	return response
}
