package veo

// VeoRequest represents the request structure for VEO video generation
type VeoRequest struct {
	Instances  []VeoInstance `json:"instances"`
	Parameters VeoParameters `json:"parameters"`
}

// VeoInstance represents an instance in the VEO request
type VeoInstance struct {
	Prompt    string    `json:"prompt,omitempty"`
	Image     *VeoImage `json:"image,omitempty"`     // 图片生成视频
	LastFrame *VeoImage `json:"lastFrame,omitempty"` // 视频垫图 - 最后一帧
	Video     *VeoVideo `json:"video,omitempty"`     // 视频延长
}

// VeoImage represents an image input for VEO
type VeoImage struct {
	BytesBase64Encoded string `json:"bytesBase64Encoded,omitempty"`
	GcsUri             string `json:"gcsUri,omitempty"`
	MimeType           string `json:"mimeType"`
}

// VeoParameters represents the parameters for VEO video generation
type VeoParameters struct {
	AspectRatio      string `json:"aspectRatio,omitempty"`
	SampleCount      int    `json:"sampleCount,omitempty"`
	DurationSeconds  string `json:"durationSeconds,omitempty"`
	PersonGeneration string `json:"personGeneration,omitempty"`
	AddWatermark     bool   `json:"addWatermark,omitempty"`
	IncludeRaiReason bool   `json:"includeRaiReason,omitempty"`
	GenerateAudio    bool   `json:"generateAudio,omitempty"`
	EnhancePrompt    bool   `json:"enhancePrompt,omitempty"`
	StorageUri       string `json:"storageUri,omitempty"`
	NegativePrompt   string `json:"negativePrompt,omitempty"`
	Seed             *int   `json:"seed,omitempty"`
}

// VeoResponse represents the response from VEO video generation
type VeoResponse struct {
	Name     string                 `json:"name"`
	Done     bool                   `json:"done"`
	Response *VeoResponseData       `json:"response,omitempty"`
	Error    *VeoError              `json:"error,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// VeoResponseData represents the response data from VEO
type VeoResponseData struct {
	Type                  string      `json:"@type"`
	Videos                []VeoVideo  `json:"videos,omitempty"`
	RaiMediaFilteredCount int         `json:"raiMediaFilteredCount,omitempty"`
	GeneratedSamples      []VeoSample `json:"generatedSamples,omitempty"` // For backward compatibility
}

// VeoVideo represents a generated video
type VeoVideo struct {
	BytesBase64Encoded string `json:"bytesBase64Encoded,omitempty"`
	GcsUri             string `json:"gcsUri,omitempty"`
	MimeType           string `json:"mimeType,omitempty"`
	Uri                string `json:"uri,omitempty"`
}

// VeoSample represents a generated sample (backward compatibility)
type VeoSample struct {
	Video VeoVideo `json:"video"`
}

// VeoError represents an error in VEO response
type VeoError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// VeoOperationRequest represents a request to check operation status
type VeoOperationRequest struct {
	OperationName string `json:"operationName"`
}
