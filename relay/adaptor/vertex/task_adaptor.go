package vertex

import (
	"bytes"
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/samber/lo"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/patrickmn/go-cache"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/constant"
	"github.com/songquanpeng/one-api/dto"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/vertexai"
	"github.com/songquanpeng/one-api/relay/channel"
	relaycommon "github.com/songquanpeng/one-api/relay/common"
	"github.com/songquanpeng/one-api/service"
)

// ApplicationDefaultCredentials ADC凭据结构
type ApplicationDefaultCredentials struct {
	Type                    string `json:"type"`
	ProjectID               string `json:"project_id"`
	PrivateKeyID            string `json:"private_key_id"`
	PrivateKey              string `json:"private_key"`
	ClientEmail             string `json:"client_email"`
	ClientID                string `json:"client_id"`
	AuthURI                 string `json:"auth_uri"`
	TokenURI                string `json:"token_uri"`
	AuthProviderX509CertURL string `json:"auth_provider_x509_cert_url"`
	ClientX509CertURL       string `json:"client_x509_cert_url"`
	UniverseDomain          string `json:"universe_domain"`
}

// Token缓存和常量
var tokenCache = cache.New(50*time.Minute, 55*time.Minute)

const defaultScope = "https://www.googleapis.com/auth/cloud-platform"

// VEORequest VEO视频生成请求
type VEORequest struct {
	Instances  []VEOInstance  `json:"instances"`
	Parameters *VEOParameters `json:"parameters,omitempty"`
}

// VEOInstance VEO实例
type VEOInstance struct {
	Prompt    string    `json:"prompt"`
	Image     *VEOImage `json:"image,omitempty"`     // 图片生成视频
	LastFrame *VEOImage `json:"lastFrame,omitempty"` // 视频垫图 - 最后一帧
	Video     *VEOVideo `json:"video,omitempty"`     // 视频延长
}

// VEOImage VEO图像
type VEOImage struct {
	BytesBase64Encoded string `json:"bytesBase64Encoded,omitempty"`
	GcsUri             string `json:"gcsUri,omitempty"`
	MimeType           string `json:"mimeType,omitempty"`
}

// VEOParameters VEO参数
type VEOParameters struct {
	DurationSeconds  int    `json:"durationSeconds,omitempty"`
	AspectRatio      string `json:"aspectRatio,omitempty"`
	SampleCount      int    `json:"sampleCount,omitempty"`
	PersonGeneration string `json:"personGeneration,omitempty"`
	EnhancePrompt    bool   `json:"enhancePrompt,omitempty"`
	AddWatermark     bool   `json:"addWatermark,omitempty"`
	IncludeRaiReason bool   `json:"includeRaiReason,omitempty"`
	GenerateAudio    bool   `json:"generateAudio,omitempty"`
}

// VEOResponse VEO响应
type VEOResponse struct {
	Name     string                 `json:"name"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	Done     bool                   `json:"done,omitempty"`
	Error    *VEOError              `json:"error,omitempty"`
	Response *VEOResult             `json:"response,omitempty"`
}

// VEOError VEO错误
type VEOError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// VEOResult VEO结果 - 支持新旧格式
type VEOResult struct {
	// 新格式：直接在response.videos中
	Videos []VEOVideo `json:"videos,omitempty"`

	// 兼容旧格式：response.generatedSamples
	GeneratedSamples []VEOGeneratedSample `json:"generatedSamples,omitempty"`

	// 最旧格式：response.predictions
	Predictions []VEOPrediction `json:"predictions,omitempty"`

	// RAI过滤信息
	RaiMediaFilteredCount int `json:"raiMediaFilteredCount,omitempty"`
}

// VEOVideo 新格式的视频结构
type VEOVideo struct {
	Uri                string `json:"uri,omitempty"`
	GcsUri             string `json:"gcsUri,omitempty"`
	MimeType           string `json:"mimeType,omitempty"`
	BytesBase64Encoded string `json:"bytesBase64Encoded,omitempty"`
}

// VEOGeneratedSample 兼容格式的生成样本
type VEOGeneratedSample struct {
	Video VEOVideo `json:"video"`
}

// VEOPrediction VEO预测结果（最旧格式）
type VEOPrediction struct {
	GeneratedVideo VEOGeneratedVideo `json:"generatedVideo"`
}

// VEOGeneratedVideo VEO生成的视频（最旧格式）
type VEOGeneratedVideo struct {
	Uri      string `json:"uri,omitempty"`
	MimeType string `json:"mimeType,omitempty"`
}

// VideoGenerationRequest 视频生成请求
type VideoGenerationRequest struct {
	Model            string `json:"model" binding:"required"`
	Prompt           string `json:"prompt" binding:"required"`
	DurationSeconds  int    `json:"duration_seconds,omitempty"`
	AspectRatio      string `json:"aspect_ratio,omitempty"`
	SampleCount      int    `json:"sample_count,omitempty"`
	PersonGeneration string `json:"person_generation,omitempty"`
	EnhancePrompt    *bool  `json:"enhance_prompt,omitempty"`
	AddWatermark     *bool  `json:"add_watermark,omitempty"`
	IncludeRaiReason *bool  `json:"include_rai_reason,omitempty"`
	GenerateAudio    *bool  `json:"generate_audio,omitempty"`
	// 垫图功能支持
	Image             *string `json:"image,omitempty"`                // 图片生成视频 (base64 或 GCS URI)
	ImageMimeType     *string `json:"image_mime_type,omitempty"`      // 图片MIME类型
	LastFrame         *string `json:"last_frame,omitempty"`           // 视频垫图-最后一帧 (base64 或 GCS URI)
	LastFrameMimeType *string `json:"last_frame_mime_type,omitempty"` // 最后一帧MIME类型
	Video             *string `json:"video,omitempty"`                // 视频延长 (base64 或 GCS URI)
	VideoMimeType     *string `json:"video_mime_type,omitempty"`      // 视频MIME类型
	NegativePrompt    *string `json:"negative_prompt,omitempty"`      // 负面提示词
	Seed              *int    `json:"seed,omitempty"`                 // 随机种子
	StorageUri        *string `json:"storage_uri,omitempty"`          // 存储URI
}

// TaskAdaptor Vertex AI 任务适配器
type TaskAdaptor struct {
	ChannelType int
	baseURL     string
	apiKey      string
	projectID   string
}

// Init 初始化适配器
func (a *TaskAdaptor) Init(info *relaycommon.TaskRelayInfo) {
	a.ChannelType = info.ChannelType
	a.baseURL = info.BaseUrl
	a.apiKey = info.ApiKey
}

// ValidateRequestAndSetAction 验证请求并设置动作
func (a *TaskAdaptor) ValidateRequestAndSetAction(c *gin.Context, info *relaycommon.TaskRelayInfo) *dto.TaskError {
	// 添加调试日志
	fmt.Printf("Vertex TaskAdaptor: ValidateRequestAndSetAction called, channelType=%d\n", info.ChannelType)

	// 设置默认动作为生成
	action := constant.TaskActionGenerate
	info.Action = action

	var req VideoGenerationRequest
	if err := common.UnmarshalBodyReusable(c, &req); err != nil {
		return service.TaskErrorWrapperLocal(err, "invalid_request", http.StatusBadRequest)
	}

	if strings.TrimSpace(req.Prompt) == "" {
		return service.TaskErrorWrapperLocal(fmt.Errorf("prompt is required"), "invalid_request", http.StatusBadRequest)
	}

	// 存储到上下文中供后续使用
	c.Set("task_request", req)
	return nil
}

// BuildRequestURL 构建请求URL
func (a *TaskAdaptor) BuildRequestURL(info *relaycommon.TaskRelayInfo) (string, error) {
	// 获取项目ID和区域 - 支持两种配置方式
	projectID, region, err := a.getProjectIDAndRegion(info)
	if err != nil {
		return "", fmt.Errorf("获取项目配置失败: %w", err)
	}

	a.projectID = projectID

	// 构建VEO模型的URL
	return fmt.Sprintf(
		"https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/google/models/%s:predictLongRunning",
		region,
		projectID,
		region,
		info.OriginModelName,
	), nil
}

// BuildRequestHeader 构建请求头
func (a *TaskAdaptor) BuildRequestHeader(c *gin.Context, req *http.Request, info *relaycommon.TaskRelayInfo) error {
	// 获取OAuth2 access token
	token, err := a.getAccessToken(c, info)
	if err != nil {
		return fmt.Errorf("获取访问令牌失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("User-Agent", "vertex-ai-sdk/1.0")
	return nil
}

// BuildRequestBody 构建请求体
func (a *TaskAdaptor) BuildRequestBody(c *gin.Context, info *relaycommon.TaskRelayInfo) (io.Reader, error) {
	v, exists := c.Get("task_request")
	if !exists {
		return nil, fmt.Errorf("request not found in context")
	}
	req := v.(VideoGenerationRequest)

	// 构建VEO请求
	instance := VEOInstance{
		Prompt: req.Prompt,
	}

	// 处理垫图功能
	if req.Image != nil && *req.Image != "" {
		// 图片生成视频
		mimeType := "image/jpeg" // 默认MIME类型
		if req.ImageMimeType != nil && *req.ImageMimeType != "" {
			mimeType = *req.ImageMimeType
		}

		veoImage := &VEOImage{
			MimeType: mimeType,
		}

		// 判断是base64还是GCS URI
		if strings.HasPrefix(*req.Image, "gs://") {
			veoImage.GcsUri = *req.Image
		} else if strings.HasPrefix(*req.Image, "data:") {
			// 提取base64数据
			parts := strings.Split(*req.Image, ",")
			if len(parts) == 2 {
				veoImage.BytesBase64Encoded = parts[1]
				// 从data URL中提取MIME类型
				if strings.HasPrefix(*req.Image, "data:") {
					mimeParts := strings.Split(parts[0], ":")
					if len(mimeParts) > 1 {
						mimeWithSemicolon := strings.Split(mimeParts[1], ";")
						if len(mimeWithSemicolon) > 0 {
							veoImage.MimeType = mimeWithSemicolon[0]
						}
					}
				}
			}
		} else {
			// 直接的base64编码
			veoImage.BytesBase64Encoded = *req.Image
		}

		instance.Image = veoImage
	}

	if req.LastFrame != nil && *req.LastFrame != "" {
		// 视频垫图 - 最后一帧
		mimeType := "image/jpeg" // 默认MIME类型
		if req.LastFrameMimeType != nil && *req.LastFrameMimeType != "" {
			mimeType = *req.LastFrameMimeType
		}

		veoLastFrame := &VEOImage{
			MimeType: mimeType,
		}

		// 判断是base64还是GCS URI
		if strings.HasPrefix(*req.LastFrame, "gs://") {
			veoLastFrame.GcsUri = *req.LastFrame
		} else if strings.HasPrefix(*req.LastFrame, "data:") {
			// 提取base64数据
			parts := strings.Split(*req.LastFrame, ",")
			if len(parts) == 2 {
				veoLastFrame.BytesBase64Encoded = parts[1]
				// 从data URL中提取MIME类型
				if strings.HasPrefix(*req.LastFrame, "data:") {
					mimeParts := strings.Split(parts[0], ":")
					if len(mimeParts) > 1 {
						mimeWithSemicolon := strings.Split(mimeParts[1], ";")
						if len(mimeWithSemicolon) > 0 {
							veoLastFrame.MimeType = mimeWithSemicolon[0]
						}
					}
				}
			}
		} else {
			// 直接的base64编码
			veoLastFrame.BytesBase64Encoded = *req.LastFrame
		}

		instance.LastFrame = veoLastFrame
	}

	if req.Video != nil && *req.Video != "" {
		// 视频延长
		mimeType := "video/mp4" // 默认MIME类型
		if req.VideoMimeType != nil && *req.VideoMimeType != "" {
			mimeType = *req.VideoMimeType
		}

		veoVideo := &VEOVideo{
			MimeType: mimeType,
		}

		// 判断是base64还是GCS URI
		if strings.HasPrefix(*req.Video, "gs://") {
			veoVideo.GcsUri = *req.Video
		} else if strings.HasPrefix(*req.Video, "data:") {
			// 提取base64数据
			parts := strings.Split(*req.Video, ",")
			if len(parts) == 2 {
				veoVideo.BytesBase64Encoded = parts[1]
				// 从data URL中提取MIME类型
				if strings.HasPrefix(*req.Video, "data:") {
					mimeParts := strings.Split(parts[0], ":")
					if len(mimeParts) > 1 {
						mimeWithSemicolon := strings.Split(mimeParts[1], ";")
						if len(mimeWithSemicolon) > 0 {
							veoVideo.MimeType = mimeWithSemicolon[0]
						}
					}
				}
			}
		} else {
			// 直接的base64编码
			veoVideo.BytesBase64Encoded = *req.Video
		}

		instance.Video = veoVideo
	}

	veoReq := VEORequest{
		Instances: []VEOInstance{instance},
		Parameters: &VEOParameters{
			DurationSeconds:  req.DurationSeconds,
			AspectRatio:      req.AspectRatio,
			SampleCount:      req.SampleCount,
			PersonGeneration: req.PersonGeneration,
		},
	}

	// 设置默认值
	if veoReq.Parameters.DurationSeconds == 0 {
		veoReq.Parameters.DurationSeconds = 8
	}
	if veoReq.Parameters.AspectRatio == "" {
		veoReq.Parameters.AspectRatio = "16:9"
	}
	if veoReq.Parameters.SampleCount == 0 {
		veoReq.Parameters.SampleCount = 1
	}
	if veoReq.Parameters.PersonGeneration == "" {
		veoReq.Parameters.PersonGeneration = "allow_all"
	}

	// 设置布尔参数
	if req.EnhancePrompt != nil {
		veoReq.Parameters.EnhancePrompt = *req.EnhancePrompt
	}
	if req.AddWatermark != nil {
		veoReq.Parameters.AddWatermark = *req.AddWatermark
	}
	if req.IncludeRaiReason != nil {
		veoReq.Parameters.IncludeRaiReason = *req.IncludeRaiReason
	}
	if req.GenerateAudio != nil {
		veoReq.Parameters.GenerateAudio = *req.GenerateAudio
	}

	data, err := json.Marshal(veoReq)
	if err != nil {
		return nil, err
	}
	return bytes.NewReader(data), nil
}

// DoRequest 执行请求
func (a *TaskAdaptor) DoRequest(c *gin.Context, info *relaycommon.TaskRelayInfo, requestBody io.Reader) (*http.Response, error) {
	return channel.DoTaskApiRequest(a, c, info, requestBody)
}

// DoResponse 处理响应
func (a *TaskAdaptor) DoResponse(c *gin.Context, resp *http.Response, info *relaycommon.TaskRelayInfo) (taskID string, taskData []byte, taskErr *dto.TaskError) {
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		taskErr = service.TaskErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError)
		return
	}

	// 解析VEO响应
	var veoResp VEOResponse
	if err := json.Unmarshal(responseBody, &veoResp); err != nil {
		taskErr = service.TaskErrorWrapper(err, "unmarshal_response_body_failed", http.StatusInternalServerError)
		return
	}

	if veoResp.Error != nil {
		taskErr = service.TaskErrorWrapper(fmt.Errorf(veoResp.Error.Message), "veo_api_error", veoResp.Error.Code)
		return
	}

	// 从操作名称中提取任务ID
	taskID = extractTaskIDFromOperationName(veoResp.Name)

	// 调试日志：检查提取的taskID
	fmt.Printf("VEO Response Name: %s\n", veoResp.Name)
	fmt.Printf("Extracted TaskID: %s\n", taskID)

	// 返回任务ID（参考new-api格式）
	c.JSON(http.StatusOK, gin.H{
		"task_id": taskID,
	})

	return taskID, responseBody, nil
}

// FetchTask 获取任务状态
func (a *TaskAdaptor) FetchTask(baseUrl, key string, body map[string]any) (*http.Response, error) {
	taskID, ok := body["task_id"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid task_id")
	}

	// 构建操作查询URL
	operationName := fmt.Sprintf("projects/%s/locations/%s/operations/%s",
		a.projectID, "us-central1", taskID)

	url := fmt.Sprintf("https://aiplatform.googleapis.com/v1/%s", operationName)

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+key)
	req.Header.Set("User-Agent", "vertex-ai-sdk/1.0")

	return http.DefaultClient.Do(req)
}

// ParseTaskResult 解析任务结果
func (a *TaskAdaptor) ParseTaskResult(respBody []byte) (*relaycommon.TaskInfo, error) {
	// 首先打印响应体以便调试
	fmt.Printf("VEO Response Body: %s\n", string(respBody))

	var veoResp VEOResponse
	err := json.Unmarshal(respBody, &veoResp)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	taskInfo := &relaycommon.TaskInfo{}
	taskInfo.TaskID = extractTaskIDFromOperationName(veoResp.Name)

	if veoResp.Error != nil {
		taskInfo.Code = veoResp.Error.Code
		taskInfo.Reason = veoResp.Error.Message
		taskInfo.Status = "FAILURE"
		return taskInfo, nil
	}

	if veoResp.Done {
		taskInfo.Status = "SUCCESS" // 这个值必须与model.TaskStatusSuccess一致

		// 处理新的响应格式
		if veoResp.Response != nil {
			// 新格式：直接在response.videos中
			if len(veoResp.Response.Videos) > 0 {
				video := veoResp.Response.Videos[0]
				if video.Uri != "" {
					taskInfo.Url = video.Uri
				}

				// 如果有base64数据，保存到本地
				if video.BytesBase64Encoded != "" {
					savedPath, err := a.saveVideoFromBase64(video.BytesBase64Encoded, video.MimeType)
					if err != nil {
						fmt.Printf("Failed to save video from base64: %v\n", err)
					} else {
						taskInfo.Url = savedPath // 使用本地路径
						fmt.Printf("Video saved to: %s\n", savedPath)
					}
				}
			} else if len(veoResp.Response.GeneratedSamples) > 0 {
				// 兼容旧格式：response.generatedSamples
				sample := veoResp.Response.GeneratedSamples[0]
				if sample.Video.Uri != "" {
					taskInfo.Url = sample.Video.Uri
				}

				// 如果有base64数据，保存到本地
				if sample.Video.BytesBase64Encoded != "" {
					savedPath, err := a.saveVideoFromBase64(sample.Video.BytesBase64Encoded, sample.Video.MimeType)
					if err != nil {
						fmt.Printf("Failed to save video from base64: %v\n", err)
					} else {
						taskInfo.Url = savedPath // 使用本地路径
						fmt.Printf("Video saved to: %s\n", savedPath)
					}
				}
			} else if len(veoResp.Response.Predictions) > 0 {
				// 最旧格式：response.predictions
				prediction := veoResp.Response.Predictions[0]
				taskInfo.Url = prediction.GeneratedVideo.Uri
			}
		}
	} else {
		taskInfo.Status = "IN_PROGRESS"
	}

	taskInfo.Code = 0
	return taskInfo, nil
}

// saveVideoFromBase64 保存base64编码的视频到本地
func (a *TaskAdaptor) saveVideoFromBase64(base64Data, mimeType string) (string, error) {
	// 解码base64数据
	videoBytes, err := base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 data: %w", err)
	}

	// 根据MIME类型确定文件扩展名
	var extension string
	switch {
	case strings.Contains(mimeType, "mp4"):
		extension = ".mp4"
	case strings.Contains(mimeType, "webm"):
		extension = ".webm"
	default:
		extension = ".mp4" // 默认
	}

	// 按日期创建目录结构: storage/videos/年/月/日/
	now := time.Now()
	videoDir := fmt.Sprintf("storage/videos/%d/%02d/%02d",
		now.Year(),
		now.Month(),
		now.Day(),
	)

	if err := os.MkdirAll(videoDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create video directory: %w", err)
	}

	// 生成唯一文件名: 时间戳_随机数.扩展名
	filename := fmt.Sprintf("veo3_video_%d_%d%s",
		now.UnixNano(),
		rand.Intn(10000),
		extension)

	// 完整的文件路径
	filePath := filepath.Join(videoDir, filename)

	// 写入文件
	if err := os.WriteFile(filePath, videoBytes, 0644); err != nil {
		return "", fmt.Errorf("failed to write video file: %w", err)
	}

	// 计算文件大小
	fileSizeMB := float64(len(videoBytes)) / (1024 * 1024)
	fmt.Printf("Video saved: %s (%.2f MB, %s)\n", filePath, fileSizeMB, mimeType)

	// 返回可访问的URL
	return fmt.Sprintf("%s/fileSystem/videos/%d/%02d/%02d/%s",
		lo.If(config.FileSystemServerAddress == "", config.ServerAddress).Else(config.FileSystemServerAddress),
		now.Year(),
		now.Month(),
		now.Day(),
		filename,
	), nil
}

// GetModelList 获取模型列表
func (a *TaskAdaptor) GetModelList() []string {
	return []string{"veo-3.0-generate-preview", "veo-2.0-generate-preview"}
}

// GetChannelName 获取渠道名称
func (a *TaskAdaptor) GetChannelName() string {
	return "vertex"
}

// getProjectIDAndRegion 获取项目ID和区域 - 支持两种配置方式
func (a *TaskAdaptor) getProjectIDAndRegion(info *relaycommon.TaskRelayInfo) (string, string, error) {
	var projectID, region string

	// 方式1: 尝试从API Key中解析凭据（如果API Key是JSON格式）
	if a.apiKey != "" {
		var creds map[string]interface{}
		if err := json.Unmarshal([]byte(a.apiKey), &creds); err == nil {
			if pid, ok := creds["project_id"].(string); ok {
				projectID = pid
				// 从凭据中获取区域（如果有）
				if r, ok := creds["region"].(string); ok {
					region = r
				}
			}
		}
	}

	// 方式2: 如果从API Key中没有获取到，尝试从Channel Config中解析
	if projectID == "" {
		// 通过ChannelId获取Channel信息
		channel, err := model.GetChannelById(info.ChannelId, true)
		if err != nil {
			return "", "", fmt.Errorf("获取渠道信息失败: %w", err)
		}

		// 解析Channel Config
		if channel.Config != "" {
			var config model.ChannelConfig
			if err := json.Unmarshal([]byte(channel.Config), &config); err == nil {
				projectID = config.VertexAIProjectID
				if region == "" {
					region = config.Region
				}
			}
		}
	}

	// 设置默认区域
	if region == "" {
		region = "us-central1"
	}

	// 验证必需的项目ID
	if projectID == "" {
		return "", "", fmt.Errorf("未找到project_id，请检查渠道配置或API密钥")
	}

	return projectID, region, nil
}

// getAccessToken 获取OAuth2访问令牌
func (a *TaskAdaptor) getAccessToken(c *gin.Context, info *relaycommon.TaskRelayInfo) (string, error) {
	// 方式1: 如果API Key不为空且不是JSON格式，直接作为token使用
	if a.apiKey != "" {
		var testJson map[string]interface{}
		if err := json.Unmarshal([]byte(a.apiKey), &testJson); err != nil {
			// 不是JSON格式，可能是直接的access token
			fmt.Printf("Using API key as direct access token\n")
			return a.apiKey, nil
		}
	}

	// 方式2: 从Channel Config中获取ADC凭据并生成token
	channel, err := model.GetChannelById(info.ChannelId, true)
	if err != nil {
		return "", fmt.Errorf("获取渠道信息失败: %w", err)
	}

	// 检查channel.Config中的VertexAIADC配置
	if channel.Config != "" {
		var config model.ChannelConfig
		if err := json.Unmarshal([]byte(channel.Config), &config); err == nil {
			if config.VertexAIADC != "" {
				fmt.Printf("Using VertexAI ADC from channel config\n")
				// 使用现有的vertexai包获取token
				ctx := context.Background()
				return vertexai.GetToken(ctx, info.ChannelId, config.VertexAIADC)
			}
		}
	}

	// 方式3: 检查API Key是否是JSON格式的ADC凭据
	if a.apiKey != "" {
		var testJson map[string]interface{}
		if err := json.Unmarshal([]byte(a.apiKey), &testJson); err == nil {
			// 是JSON格式，可能是ADC凭据
			fmt.Printf("Using API key as ADC credentials\n")
			ctx := context.Background()
			return vertexai.GetToken(ctx, info.ChannelId, a.apiKey)
		}
	}

	return "", fmt.Errorf("未找到有效的认证信息，请配置API密钥或ADC凭据")
}

// getTokenFromADC 从ADC凭据获取token（完整实现）
func (a *TaskAdaptor) getTokenFromADC(c *gin.Context, channelId int, adcJson string) (string, error) {
	cacheKey := fmt.Sprintf("vertex-task-token-%d", channelId)
	if token, found := tokenCache.Get(cacheKey); found {
		return token.(string), nil
	}

	adc := &ApplicationDefaultCredentials{}
	if err := json.Unmarshal([]byte(adcJson), adc); err != nil {
		return "", fmt.Errorf("解析ADC凭据失败: %w", err)
	}

	// 使用JWT方式获取access token
	token, err := a.getAccessTokenFromJWT(adc)
	if err != nil {
		return "", fmt.Errorf("获取access token失败: %w", err)
	}

	// 缓存token
	tokenCache.Set(cacheKey, token, cache.DefaultExpiration)
	return token, nil
}

// getAccessTokenFromJWT 使用JWT方式获取access token
func (a *TaskAdaptor) getAccessTokenFromJWT(adc *ApplicationDefaultCredentials) (string, error) {
	signedJWT, err := a.createSignedJWT(adc.ClientEmail, adc.PrivateKey)
	if err != nil {
		return "", fmt.Errorf("创建JWT失败: %w", err)
	}

	token, err := a.exchangeJwtForAccessToken(signedJWT)
	if err != nil {
		return "", fmt.Errorf("交换access token失败: %w", err)
	}

	return token, nil
}

// createSignedJWT 创建签名的JWT
func (a *TaskAdaptor) createSignedJWT(email, privateKeyStr string) (string, error) {
	// 清理私钥字符串
	privateKeyStr = strings.ReplaceAll(privateKeyStr, "\\n", "\n")

	block, _ := pem.Decode([]byte(privateKeyStr))
	if block == nil {
		return "", fmt.Errorf("解析PEM块失败")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("解析私钥失败: %w", err)
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return "", fmt.Errorf("不是RSA私钥")
	}

	now := time.Now()
	claims := jwt.MapClaims{
		"iss":   email,
		"scope": defaultScope,
		"aud":   "https://www.googleapis.com/oauth2/v4/token",
		"exp":   now.Add(time.Minute * 35).Unix(),
		"iat":   now.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	signedToken, err := token.SignedString(rsaPrivateKey)
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

// exchangeJwtForAccessToken 使用JWT交换access token
func (a *TaskAdaptor) exchangeJwtForAccessToken(signedJWT string) (string, error) {
	authURL := "https://www.googleapis.com/oauth2/v4/token"
	data := url.Values{}
	data.Set("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer")
	data.Set("assertion", signedJWT)

	resp, err := client.HTTPClient.PostForm(authURL, data)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var result map[string]any
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", err
	}

	if accessToken, ok := result["access_token"].(string); ok {
		return accessToken, nil
	}

	return "", fmt.Errorf("获取access token失败: %v", result)
}

// 辅助函数

// extractTaskIDFromOperationName 从操作名称中提取任务ID
func extractTaskIDFromOperationName(operationName string) string {
	// 操作名称格式: projects/{project}/locations/{location}/publishers/{publisher}/models/{model}/operations/{operation_id}
	// 例如: projects/mimetic-math-465309-g0/locations/us-central1/publishers/google/models/veo-3.0-generate-preview/operations/666e28ac-da9f-446b-8b0f-7210b68d213a
	parts := strings.Split(operationName, "/")
	fmt.Printf("Operation name parts: %v, length: %d\n", parts, len(parts))

	// 查找operations关键字的位置
	for i, part := range parts {
		if part == "operations" && i+1 < len(parts) {
			fmt.Printf("Found operation ID at index %d: %s\n", i+1, parts[i+1])
			return parts[i+1]
		}
	}

	// 如果没找到operations，返回整个名称作为fallback
	fmt.Printf("No operations found, returning full name: %s\n", operationName)
	return operationName
}

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return time.Now().Unix()
}
