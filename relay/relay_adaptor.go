package relay

import (
	"fmt"

	"github.com/songquanpeng/one-api/constant"
	"github.com/songquanpeng/one-api/relay/adaptor/shell"
	"github.com/songquanpeng/one-api/relay/adaptor/vertex"
	"github.com/songquanpeng/one-api/relay/channel"
	"github.com/songquanpeng/one-api/relay/channeltype"
	relaycommon "github.com/songquanpeng/one-api/relay/common"
)

func GetTaskAdaptor(platform constant.TaskPlatform) channel.TaskAdaptor {
	switch platform {
	case constant.TaskPlatformVertex:
		return &vertex.TaskAdaptor{}
	}
	return nil
}

// GetTaskAdaptorByChannelType 根据渠道类型获取任务适配器
// 这个函数考虑了链式调用的场景
func GetTaskAdaptorByChannelType(platform constant.TaskPlatform, channelType int) channel.TaskAdaptor {
	// 添加调试日志
	fmt.Printf("GetTaskAdaptorByChannelType: platform=%v, channelType=%d\n", platform, channelType)
	fmt.Printf("channeltype.ShellAPI=%d, channeltype.Custom=%d, channeltype.VertextAI=%d\n",
		channeltype.ShellAPI, channeltype.Custom, channeltype.VertextAI)

	switch platform {
	case constant.TaskPlatformVertex:
		// 根据渠道类型选择不同的适配器
		switch channelType {
		case channeltype.ShellAPI, channeltype.Custom:
			// ShellAPI 和 Custom 渠道使用转发适配器
			fmt.Printf("选择 shell.TaskAdaptor (链式调用)\n")
			return &shell.TaskAdaptor{}
		case channeltype.VertextAI:
			// VertexAI 渠道直接使用 Vertex 适配器
			fmt.Printf("选择 vertex.TaskAdaptor (直连)\n")
			return &vertex.TaskAdaptor{}
		default:
			// 临时修改：如果渠道类型未知，检查是否有 base_url 来判断是否为转发场景
			// 这是一个临时解决方案，建议修改渠道配置为正确的类型
			fmt.Printf("渠道类型未知 (%d)，检查是否为转发场景\n", channelType)
			// 如果有 base_url 且不为空，可能是转发场景
			// 注意：这里无法直接访问 info.BaseUrl，所以暂时使用 shell 适配器
			fmt.Printf("选择 shell.TaskAdaptor (临时默认 - 转发模式)\n")
			return &shell.TaskAdaptor{}
		}
	}
	return nil
}

// GetTaskAdaptorWithInfo 根据任务信息获取适配器（推荐使用）
// 注意：由于TaskRelayInfo没有Platform字段，这个函数需要额外的platform参数
func GetTaskAdaptorWithInfo(platform constant.TaskPlatform, info *relaycommon.TaskRelayInfo) channel.TaskAdaptor {
	return GetTaskAdaptorByChannelType(platform, info.ChannelType)
}
