package model

type ImageRequest struct {
	Model          string `json:"model"`
	Prompt         string `json:"prompt" binding:"required"`
	N              int    `json:"n,omitempty"`
	Size           string `json:"size,omitempty"`
	Quality        string `json:"quality,omitempty"`
	ResponseFormat string `json:"response_format,omitempty"`
	Style          string `json:"style,omitempty"`
	User           string `json:"user,omitempty"`
}

// Video represents a video input for video generation
type Video struct {
	Url                string `json:"url,omitempty"`
	BytesBase64Encoded string `json:"bytes_base64_encoded,omitempty"`
	MimeType           string `json:"mime_type,omitempty"`
}

// VideoRequest represents a video generation request
type VideoRequest struct {
	Model  string `json:"model"`
	Prompt string `json:"prompt,omitempty"`
	// 垫图功能支持
	Image     *Image `json:"image,omitempty"`      // 图片生成视频
	LastFrame *Image `json:"last_frame,omitempty"` // 视频垫图 - 最后一帧
	Video     *Video `json:"video,omitempty"`      // 视频延长
	// 其他参数
	DurationSeconds  int    `json:"duration_seconds,omitempty"`
	AspectRatio      string `json:"aspect_ratio,omitempty"`
	SampleCount      int    `json:"sample_count,omitempty"`
	PersonGeneration string `json:"person_generation,omitempty"`
	NegativePrompt   string `json:"negative_prompt,omitempty"`
	Seed             *int   `json:"seed,omitempty"`
	EnhancePrompt    *bool  `json:"enhance_prompt,omitempty"`
	AddWatermark     *bool  `json:"add_watermark,omitempty"`
	IncludeRaiReason *bool  `json:"include_rai_reason,omitempty"`
	GenerateAudio    *bool  `json:"generate_audio,omitempty"`
	StorageUri       string `json:"storage_uri,omitempty"`
	User             string `json:"user,omitempty"`
}

// Image represents an image input for video generation
type Image struct {
	BytesBase64Encoded string `json:"bytes_base64_encoded,omitempty"`
	MimeType           string `json:"mime_type,omitempty"`
	Url                string `json:"url,omitempty"`
}
